/**
 * Centralized AI Provider Configuration
 * This file contains all AI provider configuration settings and types.
 */

import { supabase } from '../lib/supabase';

// Try to import environment variables from the .env file
// If this fails, the fallback mechanism in envLoader.js will be used
let EXPO_PUBLIC_AI_PROVIDER: string | undefined;
let EXPO_PUBLIC_OLLAMA_BASE_URL: string | undefined;
let EXPO_PUBLIC_OLLAMA_MODEL: string | undefined;
let EXPO_PUBLIC_LMSTUDIO_BASE_URL: string | undefined;
let EXPO_PUBLIC_LMSTUDIO_MODEL: string | undefined;
let EXPO_PUBLIC_DEEPSEEK_API_KEY: string | undefined;
let EXPO_PUBLIC_DEEPSEEK_MODEL: string | undefined;
let EXPO_PUBLIC_OPENROUTER_API_KEY: string | undefined;
let EXPO_PUBLIC_OPENROUTER_MODEL: string | undefined;
let EXPO_PUBLIC_GEMINI_API_KEY: string | undefined;
let EXPO_PUBLIC_GEMINI_MODEL: string | undefined;

// For web environment, we'll use hardcoded defaults or process.env
// In a production app, you would use a more secure approach for sensitive values

// Use process.env if available (populated by our envLoader)
if (typeof process !== 'undefined' && process.env) {
  EXPO_PUBLIC_AI_PROVIDER = process.env.EXPO_PUBLIC_AI_PROVIDER;
  EXPO_PUBLIC_OLLAMA_BASE_URL = process.env.EXPO_PUBLIC_OLLAMA_BASE_URL;
  EXPO_PUBLIC_OLLAMA_MODEL = process.env.EXPO_PUBLIC_OLLAMA_MODEL;
  EXPO_PUBLIC_LMSTUDIO_BASE_URL = process.env.EXPO_PUBLIC_LMSTUDIO_BASE_URL;
  EXPO_PUBLIC_LMSTUDIO_MODEL = process.env.EXPO_PUBLIC_LMSTUDIO_MODEL;
  EXPO_PUBLIC_DEEPSEEK_API_KEY = process.env.EXPO_PUBLIC_DEEPSEEK_API_KEY;
  EXPO_PUBLIC_DEEPSEEK_MODEL = process.env.EXPO_PUBLIC_DEEPSEEK_MODEL;
  EXPO_PUBLIC_OPENROUTER_API_KEY = process.env.EXPO_PUBLIC_OPENROUTER_API_KEY;
  // OPENROUTER_MODEL will be fetched from the database
  EXPO_PUBLIC_GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
  EXPO_PUBLIC_GEMINI_MODEL = process.env.EXPO_PUBLIC_GEMINI_MODEL;
}

// For development, use these fallback values if nothing is set
// In production, these would come from environment variables



// Don't force the provider - use what's in the environment variables

// No default values - use only what's in the .env file

/**
 * Supported AI providers in the application
 * - ollama: Local LLM server
 * - lmstudio: Local LLM server with a different API
 * - deepseek: Cloud-based LLM provider
 * - openrouter: Aggregator for multiple LLM providers
 */
export type AIProvider = 'ollama' | 'lmstudio' | 'deepseek' | 'openrouter' | 'gemini';

/**
 * Complete AI configuration interface
 * Contains settings for all supported AI providers
 */
export interface AIConfig {
  /**
   * Currently selected AI provider
   */
  provider: AIProvider;

  /**
   * Ollama configuration
   * Used when provider is 'ollama'
   */
  ollamaConfig: {
    /** Base URL for Ollama API (e.g., http://localhost:11434) */
    baseUrl: string;
    /** Model name to use (e.g., llama3.2:latest) */
    model: string;
  };

  /**
   * LM Studio configuration
   * Used when provider is 'lmstudio'
   */
  lmstudioConfig: {
    /** Base URL for LM Studio API */
    baseUrl: string;
    /** Model name to use */
    model: string;
  };

  /**
   * DeepSeek configuration
   * Used when provider is 'deepseek'
   */
  deepseekConfig: {
    /** API key for DeepSeek */
    apiKey: string;
    /** Model name to use */
    model: string;
  };

  /**
   * OpenRouter configuration
   * Used when provider is 'openrouter'
   */
  openrouterConfig: {
    /** API key for OpenRouter */
    apiKey: string;
    /** Model name to use (e.g., deepseek/deepseek-chat:free) */
    model: string;
    /** Base URL for OpenRouter API */
    baseUrl: string;
  };

  /**
   * Gemini configuration
   * Used when provider is 'gemini'
   */
  geminiConfig: {
    /** API key for Google Gemini */
    apiKey: string;
    /** Model name to use (e.g., gemini-2.0-flash-lite) */
    model: string;
  };
}

// No default provider - use only what's in the .env file

/**
 * Complete AI configuration object created from environment variables
 * This is the main export that should be used throughout the application
 */
export const aiConfig: AIConfig = {
  /** Currently selected AI provider from .env */
  provider: EXPO_PUBLIC_AI_PROVIDER as AIProvider,

  /** Ollama configuration */
  ollamaConfig: {
    baseUrl: EXPO_PUBLIC_OLLAMA_BASE_URL || '',
    model: EXPO_PUBLIC_OLLAMA_MODEL || '',
  },

  /** LM Studio configuration */
  lmstudioConfig: {
    baseUrl: EXPO_PUBLIC_LMSTUDIO_BASE_URL || '',
    model: EXPO_PUBLIC_LMSTUDIO_MODEL || '',
  },

  /** DeepSeek configuration */
  deepseekConfig: {
    apiKey: EXPO_PUBLIC_DEEPSEEK_API_KEY || '',
    model: EXPO_PUBLIC_DEEPSEEK_MODEL || '',
  },

  /** OpenRouter configuration */
  openrouterConfig: {
    apiKey: EXPO_PUBLIC_OPENROUTER_API_KEY || '',
    model: EXPO_PUBLIC_OPENROUTER_MODEL || '', // Will be updated from database
    baseUrl: 'https://openrouter.ai/api/v1', // Standard OpenRouter base URL
  },

  /** Gemini configuration */
  geminiConfig: {
    apiKey: EXPO_PUBLIC_GEMINI_API_KEY || '',
    model: EXPO_PUBLIC_GEMINI_MODEL || '',
  },
};

// Validate configuration and warn about missing required values
if (aiConfig.provider === 'deepseek' && !aiConfig.deepseekConfig.apiKey) {
  console.warn('DeepSeek API key is not set. Please set EXPO_PUBLIC_DEEPSEEK_API_KEY in your .env file.');
}
if (aiConfig.provider === 'openrouter' && !aiConfig.openrouterConfig.apiKey) {
  console.warn('OpenRouter API key is not set. Please set EXPO_PUBLIC_OPENROUTER_API_KEY in your .env file.');
}
if (aiConfig.provider === 'gemini' && !aiConfig.geminiConfig.apiKey) {
  console.warn('Gemini API key is not set. Please set EXPO_PUBLIC_GEMINI_API_KEY in your .env file.');
}

/**
 * Individual exports for backward compatibility
 * These allow existing code to continue working without changes
 * New code should use the aiConfig object directly
 */

/** Currently selected AI provider */
export const AI_PROVIDER = aiConfig.provider;

/** Ollama base URL */
export const OLLAMA_BASE_URL = aiConfig.ollamaConfig.baseUrl;

/** Ollama model name */
export const OLLAMA_MODEL = aiConfig.ollamaConfig.model;

/** LM Studio base URL */
export const LMSTUDIO_BASE_URL = aiConfig.lmstudioConfig.baseUrl;

/** LM Studio model name */
export const LMSTUDIO_MODEL = aiConfig.lmstudioConfig.model;

/** DeepSeek API key */
export const DEEPSEEK_API_KEY = aiConfig.deepseekConfig.apiKey;

/** DeepSeek model name */
export const DEEPSEEK_MODEL = aiConfig.deepseekConfig.model;

/** OpenRouter API key */
export let OPENROUTER_API_KEY = aiConfig.openrouterConfig.apiKey;

/** OpenRouter model name */
export let OPENROUTER_MODEL = aiConfig.openrouterConfig.model;

/** Gemini API key */
export const GEMINI_API_KEY = aiConfig.geminiConfig.apiKey;

/** Gemini model name */
export const GEMINI_MODEL = aiConfig.geminiConfig.model;

/**
 * Fetches configuration values from the app_config table
 * @returns Promise that resolves when all config values are loaded
 */
export async function loadDatabaseConfig(): Promise<void> {
  // Only load additional configuration if the active provider is OpenRouter
  // This prevents unnecessary database queries and ensures that providers
  // other than OpenRouter exclusively rely on their .env configuration.
  if (aiConfig.provider !== 'openrouter') {
    return;
  }
  try {
    // Fetch both OPENROUTER_MODEL and OPENROUTER_API_KEY in a single query
    const { data: configData, error } = await supabase
      .from('app_config')
      .select('key, value')
      .in('key', ['EXPO_PUBLIC_OPENROUTER_MODEL', 'EXPO_PUBLIC_OPENROUTER_API_KEY']);

    if (error) {
      // Propagate the error to the caller to be handled by the UI
      throw error;
    }

    // Process the results
    if (configData && configData.length > 0) {
      let modelLoaded = false;
      let apiKeyLoaded = false;

      configData.forEach(item => {
        if (item.key === 'EXPO_PUBLIC_OPENROUTER_MODEL' && item.value) {
          // Update both the exported constant and the aiConfig object for model
          OPENROUTER_MODEL = item.value;
          aiConfig.openrouterConfig.model = item.value;
          modelLoaded = true;
        } else if (item.key === 'EXPO_PUBLIC_OPENROUTER_API_KEY' && item.value) {
          // Update both the exported constant and the aiConfig object for API key
          OPENROUTER_API_KEY = item.value;
          aiConfig.openrouterConfig.apiKey = item.value;
          apiKeyLoaded = true;
        }
      });

      if (!modelLoaded || !apiKeyLoaded) {
      }
    } else {
    }
  } catch (error) {
    console.error('Configuration load error');
    // Re-throw a generic error without sensitive details
    throw new Error('Failed to load configuration');
  }
}
