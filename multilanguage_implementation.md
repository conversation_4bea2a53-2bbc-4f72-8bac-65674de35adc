
# Multilanguage Implementation Plan

This document outlines a step-by-step plan to add multilanguage support to the application, allowing users to select their preferred UI language from English, Albanian, German, Italian, French, and Spanish.

## Phase 1: Setup and Core Integration

1.  **Assess Current String Management:**
    *   Identify all hardcoded strings in the UI components (e.g., `App.tsx`, `app/`, `src/components/`).
    *   Determine if any existing mechanism for text display is in place that could be leveraged or needs to be replaced.

2.  **Choose a Localization Library:**
    *   **Recommendation:** `react-i18next` combined with `expo-localization` for detecting the device's locale and `i18next-chained-backend` for managing translation files.
    *   **Reasoning:** `react-i18next` is a widely used and robust internationalization framework for React, and `expo-localization` provides native device locale information.

3.  **Install Dependencies:**
    *   Add `react-i18next`, `i18next`, `expo-localization`, and `i18next-chained-backend` to `package.json`.

4.  **Prepare Translation Files Structure:**
    *   Create a new directory, e.g., `src/locales/`.
    *   Inside `src/locales/`, create subdirectories for each language: `en/`, `sq/`, `de/`, `it/`, `fr/`, `es/`.
    *   Within each language directory, create a `translation.json` file (or similar, e.g., `common.json`, `settings.json` for modularity).
    *   **Example `src/locales/en/translation.json`:**
        ```json
        {
          "welcomeScreen": {
            "title": "Welcome",
            "description": "Your journey to well-being starts here."
          },
          "settingsScreen": {
            "languageSetting": "Language"
          }
        }
        ```

5.  **Initialize and Configure `i18next`:**
    *   Create a new file, e.g., `src/config/i18n.ts`.
    *   Configure `i18next` to load translations from the created files, detect the user's preferred language using `expo-localization`, and fall back to English if a translation is missing.
    *   **Key configuration points:**
        *   `detection`: Use `expo-localization` to get the device's preferred language.
        *   `resources`: Point to the `translation.json` files for each language.
        *   `fallbackLng`: Set to `en`.
        *   `interpolation`: Configure for React Native (e.g., `escapeValue: false`).

## Phase 2: UI Integration and Language Selection

6.  **Integrate `i18next` with React Components:**
    *   Wrap the root component of the application (e.g., in `App.tsx` or `index.ts`) with `I18nextProvider` from `react-i18next`.
    *   Use the `useTranslation` hook or `withTranslation` HOC in components to access translated strings.
    *   **Example:** Replace `<Text>Welcome</Text>` with `<Text>{t('welcomeScreen.title')}</Text>`.

7.  **Add Language Selection to Settings Screen (`app/settings.tsx`):**
    *   Locate `app/settings.tsx`.
    *   Add a new UI element (e.g., a `Picker` or `Dropdown` component) that lists the supported languages: English, Albanian, German, Italian, French, Spanish.
    *   **Map languages to codes:** `en` (English), `sq` (Albanian), `de` (German), `it` (Italian), `fr` (French), `es` (Spanish).

8.  **Implement Language Switching Logic:**
    *   When a user selects a language from the dropdown:
        *   Call `i18n.changeLanguage(selectedLanguageCode)` to dynamically change the application's language.
        *   Persist the selected language preference using `AsyncStorage` (or a similar persistent storage solution) so it remains active across app sessions.
        *   On app startup, read the stored language preference and apply it using `i18n.changeLanguage()`.

## Phase 3: Testing and Refinement

9.  **Translate All UI Strings:**
    *   Go through all UI components and replace hardcoded strings with their `i18n` keys.
    *   Populate the `translation.json` files for all supported languages with the corresponding translated text.

10. **Testing:**
    *   **Manual Testing:** Test language switching on different devices/emulators.
    *   **Automated Testing (Optional but Recommended):** If unit/integration tests are in place, add tests to verify that text changes correctly when the language is switched.

11. **Font Support (Consideration):**
    *   Verify that the chosen font supports all characters required for Albanian, German, Italian, French, and Spanish. If not, consider using a font that does or implementing font-per-language logic.

## Approval

This plan is ready for your review and approval. No code changes will be made until approval is granted.