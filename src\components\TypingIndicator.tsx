import React, { memo } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { COLORS } from '../lib/constants';

interface TypingIndicatorProps {
  isVisible: boolean;
  colors: any;
}

/**
 * TypingIndicator component displays a typing indicator when the AI is generating a response
 */
const TypingIndicator = memo(({ isVisible, colors }: TypingIndicatorProps) => {
  if (!isVisible) return null;

  return (
    <View style={[styles.typingIndicatorContainer, { backgroundColor: colors.surfaceLight }]}>
      <ActivityIndicator size="small" color={colors.textSecondary} />
      <Text style={[styles.typingIndicatorText, { color: colors.textSecondary }]}>
        Companion is thinking...
      </Text>
    </View>
  );
});

const styles = StyleSheet.create({
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: COLORS.gray[100],
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  typingIndicatorText: {
    marginLeft: 8,
    fontSize: 12,
    color: COLORS.gray[600],
    fontStyle: 'italic',
  },
});

export default TypingIndicator;
