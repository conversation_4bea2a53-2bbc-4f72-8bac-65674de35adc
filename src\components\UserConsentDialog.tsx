import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
  Platform,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useUserConsent } from '../contexts/UserConsentContext';
import { Ionicons } from '@expo/vector-icons';

export const UserConsentDialog = () => {
  const { colors, isDark } = useTheme();
  const { showConsentDialog, setShowConsentDialog, giveConsent } = useUserConsent();

  const [dataCollectionConsent, setDataCollectionConsent] = useState(true);
  const [aiStorageConsent, setAiStorageConsent] = useState(true);

  const handleAccept = async () => {
    await giveConsent(dataCollectionConsent, aiStorageConsent);
  };

  const themed = {
    borderLight: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
    accentBg: isDark ? 'rgba(6,182,212,0.15)' : 'rgba(6,182,212,0.1)',
    cardBg: isDark ? '#1A2634' : '#FFFFFF',
    shadowColor: isDark ? '#000000' : '#000000',
    shadowOpacity: isDark ? 0.4 : 0.1,
  };

  if (!showConsentDialog) {
    return null;
  }

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={showConsentDialog}
      onRequestClose={() => {
        // We don't allow closing without consent
      }}
    >
      <View style={styles.centeredView}>
        <View style={[styles.modalView, { backgroundColor: themed.cardBg, shadowColor: themed.shadowColor, shadowOpacity: themed.shadowOpacity }]}>
          <View style={styles.headerContainer}>
            <Ionicons name="shield-checkmark-outline" size={32} color={colors.accent} />
            <Text style={[styles.title, { color: colors.text }]}>Data Privacy Consent</Text>
          </View>

          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            <Text style={[styles.description, { color: colors.text }]}>
              To provide you with the best experience, Verbalyze needs your consent for the following:
            </Text>

            <View style={[styles.consentItem, { borderBottomColor: themed.borderLight }]}>
              <View style={styles.consentTextContainer}>
                <Text style={[styles.consentTitle, { color: colors.text }]}>Error Reporting</Text>
                <Text style={[styles.consentDescription, { color: colors.textSecondary }]}>
                  We collect basic error reports to help improve app stability. This only includes technical
                  information when the app encounters problems. No usage data or app interactions are tracked
                  or collected. Your privacy is our priority.
                </Text>
              </View>
              <Switch
                value={dataCollectionConsent}
                onValueChange={setDataCollectionConsent}
                trackColor={{ false: '#767577', true: colors.accentLight }}
                thumbColor={dataCollectionConsent ? colors.accent : '#f4f3f4'}
                ios_backgroundColor="#3e3e3e"
              />
            </View>

            <View style={[styles.consentItem, { borderBottomColor: themed.borderLight }]}>
              <View style={styles.consentTextContainer}>
                <Text style={[styles.consentTitle, { color: colors.text }]}>AI Conversation Storage</Text>
                <Text style={[styles.consentDescription, { color: colors.textSecondary }]}>
                  We securely store your conversations with AI companions to provide conversation history.
                  All messages are encrypted before storage for your privacy. You can view your conversation
                  history in the app and delete it at any time from the settings.
                </Text>
              </View>
              <Switch
                value={aiStorageConsent}
                onValueChange={setAiStorageConsent}
                trackColor={{ false: '#767577', true: colors.accentLight }}
                thumbColor={aiStorageConsent ? colors.accent : '#f4f3f4'}
                ios_backgroundColor="#3e3e3e"
              />
            </View>

            <View style={styles.disclaimerContainer}>
              <Text style={[styles.disclaimer, { color: colors.textSecondary }]}>
                You can change these preferences later in the app settings. By continuing, you also agree to our{' '}
                <Text style={[styles.link, { color: colors.accent }]}>Privacy Policy</Text> and{' '}
                <Text style={[styles.link, { color: colors.accent }]}>Terms of Service</Text>.
              </Text>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, { backgroundColor: colors.accent }]}
                onPress={handleAccept}
              >
                <Text style={styles.buttonText}>Accept & Continue</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalView: {
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 20,
    padding: 20,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowRadius: 4,
    elevation: 5,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 10,
    textAlign: 'center',
  },
  scrollView: {
    width: '100%',
  },
  description: {
    fontSize: 16,
    marginBottom: 20,
    lineHeight: 22,
  },
  consentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  consentTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  consentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  consentDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  disclaimerContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  disclaimer: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  link: {
    textDecorationLine: 'underline',
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
    minWidth: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UserConsentDialog;
